"""
地理投影系统使用示例
演示如何使用高性能地理特征投影和检测系统
"""

import numpy as np
import cv2
from geo_projection_system import (
    GeoProjectionSystem, CameraParams, DroneState, FeatureStyle, FeatureType,
    DetectionResult, create_camera_params, create_drone_state, create_detection
)
import time


def create_sample_data():
    """创建示例数据"""
    
    # 1. 相机参数 (示例：大疆无人机相机)
    camera_params = create_camera_params(
        fx=1000.0, fy=1000.0,  # 焦距
        cx=640.0, cy=360.0,    # 主点
        width=1280, height=720  # 图像尺寸
    )
    
    # 2. 无人机状态
    drone_state = create_drone_state(
        lat=39.9042,    # 北京天安门纬度
        lon=116.4074,   # 北京天安门经度
        alt=100.0,      # 100米高度
        pitch=-30.0,    # 向下30度
        yaw=0.0,        # 正北方向
        roll=0.0        # 无滚转
    )
    
    # 3. 地理特征
    # 白名单多边形区域 (天安门广场周围)
    whitelist_polygon = [
        (39.9050, 116.4060),  # 西北角
        (39.9050, 116.4088),  # 东北角
        (39.9034, 116.4088),  # 东南角
        (39.9034, 116.4060),  # 西南角
    ]
    
    # 巡检轨迹线
    patrol_trajectory = [
        (39.9048, 116.4062),
        (39.9046, 116.4070),
        (39.9042, 116.4078),
        (39.9038, 116.4086),
        (39.9036, 116.4084),
    ]
    
    # 禁飞边界线
    no_fly_boundary = [
        (39.9055, 116.4055),
        (39.9055, 116.4093),
        (39.9029, 116.4093),
        (39.9029, 116.4055),
    ]
    
    # 4. 模拟检测结果
    detections = [
        create_detection((100, 150, 200, 250), 0.95, 0),  # 人员
        create_detection((300, 200, 400, 300), 0.87, 1),  # 车辆
        create_detection((500, 100, 600, 200), 0.92, 0),  # 人员
        create_detection((700, 300, 800, 400), 0.78, 2),  # 其他目标
    ]
    
    return camera_params, drone_state, whitelist_polygon, patrol_trajectory, no_fly_boundary, detections


def setup_projection_system(camera_params):
    """设置投影系统"""
    
    # 创建投影系统
    projection_system = GeoProjectionSystem(camera_params, max_workers=4)
    
    return projection_system


def add_features_to_system(projection_system, whitelist_polygon, patrol_trajectory, no_fly_boundary):
    """添加特征到系统"""
    
    # 添加白名单多边形
    whitelist_style = FeatureStyle(
        color=(0, 255, 0),      # 绿色
        thickness=3,
        fill=True,
        fill_color=(0, 255, 0),
        alpha=0.2
    )
    projection_system.add_feature(
        coordinates=whitelist_polygon,
        feature_type=FeatureType.POLYGON,
        style=whitelist_style,
        name="whitelist_area"
    )
    
    # 添加巡检轨迹
    patrol_style = FeatureStyle(
        color=(255, 255, 0),    # 黄色
        thickness=4,
        show_direction=True,
        buffer_distance=25.0
    )
    projection_system.add_feature(
        coordinates=patrol_trajectory,
        feature_type=FeatureType.TRAJECTORY,
        style=patrol_style,
        name="patrol_route"
    )
    
    # 添加禁飞边界
    boundary_style = FeatureStyle(
        color=(0, 0, 255),      # 红色
        thickness=5,
        buffer_distance=50.0
    )
    projection_system.add_feature(
        coordinates=no_fly_boundary,
        feature_type=FeatureType.BOUNDARY,
        style=boundary_style,
        name="no_fly_zone"
    )


def process_single_frame(projection_system, image, detections, drone_state):
    """处理单帧图像"""
    
    print("开始处理帧...")
    start_time = time.time()
    
    # 处理帧
    result_image, detection_results = projection_system.process_frame(
        image, detections, drone_state
    )
    
    elapsed = time.time() - start_time
    print(f"帧处理完成，耗时: {elapsed:.3f}s")
    
    # 打印检测结果
    print("\n检测结果分析:")
    for i, result in enumerate(detection_results):
        detection = result['detection']
        matches = result['feature_matches']
        
        print(f"检测 {i+1}: 置信度={detection.confidence:.2f}, 中心=({detection.center[0]}, {detection.center[1]})")
        
        if matches:
            print(f"  匹配特征: {[m['name'] for m in matches]}")
        else:
            print("  无匹配特征")
    
    return result_image, detection_results


def performance_test(projection_system, image, detections, drone_state, num_iterations=100):
    """性能测试"""
    
    print(f"\n开始性能测试 ({num_iterations} 次迭代)...")
    
    times = []
    for i in range(num_iterations):
        start_time = time.time()
        projection_system.process_frame(image, detections, drone_state)
        elapsed = time.time() - start_time
        times.append(elapsed)
        
        if (i + 1) % 20 == 0:
            print(f"完成 {i + 1}/{num_iterations} 次迭代")
    
    # 统计结果
    times = np.array(times)
    print(f"\n性能测试结果:")
    print(f"平均处理时间: {np.mean(times):.4f}s")
    print(f"最小处理时间: {np.min(times):.4f}s")
    print(f"最大处理时间: {np.max(times):.4f}s")
    print(f"标准差: {np.std(times):.4f}s")
    print(f"平均FPS: {1.0 / np.mean(times):.1f}")


def main():
    """主函数"""
    
    print("=== 高性能地理投影系统示例 ===\n")
    
    # 1. 创建示例数据
    print("1. 创建示例数据...")
    camera_params, drone_state, whitelist_polygon, patrol_trajectory, no_fly_boundary, detections = create_sample_data()
    
    # 2. 设置投影系统
    print("2. 设置投影系统...")
    projection_system = setup_projection_system(camera_params)
    
    # 3. 添加特征
    print("3. 添加地理特征...")
    add_features_to_system(projection_system, whitelist_polygon, patrol_trajectory, no_fly_boundary)
    
    # 4. 创建测试图像
    print("4. 创建测试图像...")
    image = np.zeros((camera_params.height, camera_params.width, 3), dtype=np.uint8)
    image.fill(50)  # 深灰色背景
    
    # 5. 处理单帧
    print("5. 处理单帧图像...")
    result_image, detection_results = process_single_frame(
        projection_system, image, detections, drone_state
    )
    
    # 6. 性能测试
    print("6. 执行性能测试...")
    performance_test(projection_system, image, detections, drone_state, num_iterations=50)
    
    # 7. 保存结果图像
    print("\n7. 保存结果图像...")
    cv2.imwrite("result_image.jpg", result_image)
    print("结果图像已保存为 result_image.jpg")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()
