"""
高性能地理特征投影和检测系统
工业级实现，优化运行速度和内存使用
"""

import numpy as np
import cv2
from typing import List, Tuple, Dict, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import numba
from numba import jit, prange
import logging
from concurrent.futures import ThreadPoolExecutor
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FeatureType(Enum):
    """特征类型枚举"""
    POLYGON = "polygon"
    TRAJECTORY = "trajectory"
    BOUNDARY = "boundary"
    PATH = "path"


@dataclass
class CameraParams:
    """相机参数"""
    fx: float
    fy: float
    cx: float
    cy: float
    width: int
    height: int
    distortion: Optional[np.ndarray] = None


@dataclass
class DroneState:
    """无人机状态"""
    latitude: float
    longitude: float
    altitude: float  # 相对海拔高度
    pitch: float = 0.0  # 俯仰角 (弧度)
    yaw: float = 0.0    # 偏航角 (弧度)
    roll: float = 0.0   # 滚转角 (弧度)


@dataclass
class FeatureStyle:
    """特征样式配置"""
    color: Tuple[int, int, int] = (0, 255, 0)
    thickness: int = 2
    fill: bool = False
    fill_color: Optional[Tuple[int, int, int]] = None
    alpha: float = 0.3
    show_direction: bool = False
    buffer_distance: float = 20.0


@dataclass
class GeoFeature:
    """地理特征"""
    coordinates: List[Tuple[float, float]]  # [(lat, lon), ...]
    feature_type: FeatureType
    style: FeatureStyle = field(default_factory=FeatureStyle)
    name: str = ""
    pixel_coords: List[Tuple[int, int]] = field(default_factory=list, init=False)
    is_valid: bool = field(default=False, init=False)


@dataclass
class DetectionResult:
    """检测结果"""
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    confidence: float
    class_id: int
    center: Tuple[int, int] = field(init=False)
    
    def __post_init__(self):
        self.center = (
            (self.bbox[0] + self.bbox[2]) // 2,
            (self.bbox[1] + self.bbox[3]) // 2
        )


# 使用Numba JIT编译的高性能函数
@jit(nopython=True, cache=True)
def geo_to_pixel_fast(lat: float, lon: float, 
                     drone_lat: float, drone_lon: float, drone_alt: float,
                     fx: float, fy: float, cx: float, cy: float,
                     pitch: float, yaw: float, roll: float) -> Tuple[bool, float, float]:
    """
    高性能地理坐标到像素坐标转换
    返回: (是否有效, x, y)
    """
    # 地理坐标转世界坐标 (简化投影)
    lat_rad = np.radians(drone_lat)
    dx = (lon - drone_lon) * 111320.0 * np.cos(lat_rad)
    dy = (lat - drone_lat) * 111320.0
    dz = -drone_alt  # 假设地面高度为0
    
    # 构建旋转矩阵 (ZYX欧拉角)
    cos_yaw, sin_yaw = np.cos(yaw), np.sin(yaw)
    cos_pitch, sin_pitch = np.cos(pitch), np.sin(pitch)
    cos_roll, sin_roll = np.cos(roll), np.sin(roll)
    
    # 旋转矩阵计算
    r11 = cos_yaw * cos_pitch
    r12 = cos_yaw * sin_pitch * sin_roll - sin_yaw * cos_roll
    r13 = cos_yaw * sin_pitch * cos_roll + sin_yaw * sin_roll
    r21 = sin_yaw * cos_pitch
    r22 = sin_yaw * sin_pitch * sin_roll + cos_yaw * cos_roll
    r23 = sin_yaw * sin_pitch * cos_roll - cos_yaw * sin_roll
    r31 = -sin_pitch
    r32 = cos_pitch * sin_roll
    r33 = cos_pitch * cos_roll
    
    # 世界坐标转相机坐标
    cam_x = r11 * dx + r12 * dy + r13 * dz
    cam_y = r21 * dx + r22 * dy + r23 * dz
    cam_z = r31 * dx + r32 * dy + r33 * dz
    
    # 检查是否在相机前方
    if cam_z <= 0:
        return False, 0.0, 0.0
    
    # 相机坐标转像素坐标
    x_norm = cam_x / cam_z
    y_norm = cam_y / cam_z
    
    u = fx * x_norm + cx
    v = fy * y_norm + cy
    
    return True, u, v


@jit(nopython=True, cache=True)
def point_in_polygon_fast(px: float, py: float, 
                         polygon_x: np.ndarray, polygon_y: np.ndarray) -> bool:
    """
    高性能点在多边形判断 (射线法)
    """
    n = len(polygon_x)
    inside = False
    
    j = n - 1
    for i in range(n):
        xi, yi = polygon_x[i], polygon_y[i]
        xj, yj = polygon_x[j], polygon_y[j]
        
        if ((yi > py) != (yj > py)) and (px < (xj - xi) * (py - yi) / (yj - yi) + xi):
            inside = not inside
        j = i
    
    return inside


@jit(nopython=True, cache=True)
def point_to_line_distance_fast(px: float, py: float,
                               x1: float, y1: float, x2: float, y2: float) -> float:
    """
    高性能点到线段距离计算
    """
    dx = x2 - x1
    dy = y2 - y1
    
    if dx == 0 and dy == 0:
        return np.sqrt((px - x1) ** 2 + (py - y1) ** 2)
    
    t = ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)
    t = max(0, min(1, t))
    
    proj_x = x1 + t * dx
    proj_y = y1 + t * dy
    
    return np.sqrt((px - proj_x) ** 2 + (py - proj_y) ** 2)


@jit(nopython=True, cache=True, parallel=True)
def batch_geo_to_pixel(coords_lat: np.ndarray, coords_lon: np.ndarray,
                      drone_lat: float, drone_lon: float, drone_alt: float,
                      fx: float, fy: float, cx: float, cy: float,
                      pitch: float, yaw: float, roll: float,
                      img_width: int, img_height: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    批量地理坐标转换 (并行处理)
    返回: (有效性数组, x坐标数组, y坐标数组)
    """
    n = len(coords_lat)
    valid = np.zeros(n, dtype=np.bool_)
    x_coords = np.zeros(n, dtype=np.float32)
    y_coords = np.zeros(n, dtype=np.float32)
    
    for i in prange(n):
        is_valid, x, y = geo_to_pixel_fast(
            coords_lat[i], coords_lon[i],
            drone_lat, drone_lon, drone_alt,
            fx, fy, cx, cy, pitch, yaw, roll
        )
        
        # 检查是否在图像范围内
        if is_valid and 0 <= x < img_width and 0 <= y < img_height:
            valid[i] = True
            x_coords[i] = x
            y_coords[i] = y
    
    return valid, x_coords, y_coords


class GeoProjectionSystem:
    """高性能地理投影系统"""
    
    def __init__(self, camera_params: CameraParams, max_workers: int = 4):
        self.camera_params = camera_params
        self.features: List[GeoFeature] = []
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 预分配内存池
        self._coord_buffer = np.zeros((1000, 2), dtype=np.float64)
        self._pixel_buffer = np.zeros((1000, 2), dtype=np.float32)
        
        logger.info(f"初始化地理投影系统，最大工作线程: {max_workers}")
    
    def add_feature(self, coordinates: List[Tuple[float, float]], 
                   feature_type: FeatureType, style: Optional[FeatureStyle] = None,
                   name: str = "") -> GeoFeature:
        """添加地理特征"""
        if style is None:
            style = FeatureStyle()
        
        feature = GeoFeature(
            coordinates=coordinates,
            feature_type=feature_type,
            style=style,
            name=name
        )
        self.features.append(feature)
        return feature
    
    def project_features(self, drone_state: DroneState) -> List[GeoFeature]:
        """投影所有特征到图像坐标"""
        start_time = time.time()
        valid_features = []
        
        for feature in self.features:
            if self._project_single_feature(feature, drone_state):
                valid_features.append(feature)
        
        elapsed = time.time() - start_time
        logger.debug(f"投影 {len(self.features)} 个特征耗时: {elapsed:.3f}s")
        
        return valid_features

    def _project_single_feature(self, feature: GeoFeature, drone_state: DroneState) -> bool:
        """投影单个特征"""
        if not feature.coordinates:
            return False

        # 准备坐标数组
        n_coords = len(feature.coordinates)
        if n_coords > len(self._coord_buffer):
            self._coord_buffer = np.zeros((n_coords * 2, 2), dtype=np.float64)

        coords_lat = np.array([coord[0] for coord in feature.coordinates], dtype=np.float64)
        coords_lon = np.array([coord[1] for coord in feature.coordinates], dtype=np.float64)

        # 批量投影
        valid, x_coords, y_coords = batch_geo_to_pixel(
            coords_lat, coords_lon,
            drone_state.latitude, drone_state.longitude, drone_state.altitude,
            self.camera_params.fx, self.camera_params.fy,
            self.camera_params.cx, self.camera_params.cy,
            drone_state.pitch, drone_state.yaw, drone_state.roll,
            self.camera_params.width, self.camera_params.height
        )

        # 提取有效坐标
        valid_indices = np.where(valid)[0]
        if len(valid_indices) < 2:
            feature.is_valid = False
            return False

        feature.pixel_coords = [
            (int(x_coords[i]), int(y_coords[i])) for i in valid_indices
        ]
        feature.is_valid = True
        return True

    def draw_features(self, image: np.ndarray, valid_features: List[GeoFeature]) -> np.ndarray:
        """绘制所有特征"""
        result = image.copy()

        for feature in valid_features:
            result = self._draw_single_feature(result, feature)

        return result

    def _draw_single_feature(self, image: np.ndarray, feature: GeoFeature) -> np.ndarray:
        """绘制单个特征"""
        if not feature.pixel_coords or len(feature.pixel_coords) < 2:
            return image

        pts = np.array(feature.pixel_coords, dtype=np.int32)
        color = feature.style.color
        thickness = feature.style.thickness

        if feature.feature_type == FeatureType.POLYGON:
            # 绘制多边形
            cv2.polylines(image, [pts], True, color, thickness, cv2.LINE_AA)

            # 填充
            if feature.style.fill:
                fill_color = feature.style.fill_color or color
                overlay = image.copy()
                cv2.fillPoly(overlay, [pts], fill_color)
                cv2.addWeighted(overlay, feature.style.alpha, image, 1 - feature.style.alpha, 0, image)

        else:  # 轨迹线、边界线等
            cv2.polylines(image, [pts], False, color, thickness, cv2.LINE_AA)

            # 方向箭头
            if feature.style.show_direction and len(feature.pixel_coords) >= 2:
                self._draw_direction_arrows(image, feature.pixel_coords, color)

        return image

    def _draw_direction_arrows(self, image: np.ndarray, points: List[Tuple[int, int]],
                              color: Tuple[int, int, int]):
        """绘制方向箭头"""
        arrow_length = 15
        arrow_angle = np.pi / 6

        for i in range(0, len(points) - 1, max(1, len(points) // 5)):  # 每隔几个点绘制一个箭头
            p1 = np.array(points[i], dtype=np.float32)
            p2 = np.array(points[i + 1], dtype=np.float32)

            # 计算方向向量
            direction = p2 - p1
            length = np.linalg.norm(direction)
            if length < 1:
                continue

            direction = direction / length

            # 箭头顶点
            arrow_tip = p2

            # 箭头两翼
            wing1 = arrow_tip - arrow_length * (
                direction * np.cos(arrow_angle) +
                np.array([-direction[1], direction[0]]) * np.sin(arrow_angle)
            )
            wing2 = arrow_tip - arrow_length * (
                direction * np.cos(arrow_angle) -
                np.array([-direction[1], direction[0]]) * np.sin(arrow_angle)
            )

            # 绘制箭头
            cv2.line(image, tuple(arrow_tip.astype(int)), tuple(wing1.astype(int)), color, 2)
            cv2.line(image, tuple(arrow_tip.astype(int)), tuple(wing2.astype(int)), color, 2)

    def check_detections(self, detections: List[DetectionResult],
                        valid_features: List[GeoFeature]) -> List[Dict]:
        """检查检测结果与特征的空间关系"""
        results = []

        for detection in detections:
            result = {
                'detection': detection,
                'feature_matches': []
            }

            for feature in valid_features:
                if self._check_detection_in_feature(detection, feature):
                    result['feature_matches'].append({
                        'feature': feature,
                        'type': feature.feature_type.value,
                        'name': feature.name
                    })

            results.append(result)

        return results

    def _check_detection_in_feature(self, detection: DetectionResult,
                                   feature: GeoFeature) -> bool:
        """检查检测结果是否在特征内"""
        if not feature.pixel_coords or len(feature.pixel_coords) < 2:
            return False

        center_x, center_y = detection.center

        if feature.feature_type == FeatureType.POLYGON:
            # 多边形内点判断
            polygon_x = np.array([p[0] for p in feature.pixel_coords], dtype=np.float64)
            polygon_y = np.array([p[1] for p in feature.pixel_coords], dtype=np.float64)
            return point_in_polygon_fast(center_x, center_y, polygon_x, polygon_y)

        else:
            # 轨迹线距离判断
            threshold = feature.style.buffer_distance
            for i in range(len(feature.pixel_coords) - 1):
                p1 = feature.pixel_coords[i]
                p2 = feature.pixel_coords[i + 1]
                distance = point_to_line_distance_fast(center_x, center_y, p1[0], p1[1], p2[0], p2[1])
                if distance <= threshold:
                    return True
            return False

    def process_frame(self, image: np.ndarray, detections: List[DetectionResult],
                     drone_state: DroneState) -> Tuple[np.ndarray, List[Dict]]:
        """处理单帧图像的完整流程"""
        start_time = time.time()

        # 1. 投影特征
        valid_features = self.project_features(drone_state)

        # 2. 绘制特征
        result_image = self.draw_features(image, valid_features)

        # 3. 检查检测结果
        detection_results = self.check_detections(detections, valid_features)

        # 4. 绘制检测结果状态
        result_image = self._draw_detection_status(result_image, detection_results)

        # 5. 添加统计信息
        result_image = self._add_statistics(result_image, detection_results, valid_features)

        elapsed = time.time() - start_time
        logger.debug(f"处理帧耗时: {elapsed:.3f}s")

        return result_image, detection_results

    def _draw_detection_status(self, image: np.ndarray, results: List[Dict]) -> np.ndarray:
        """绘制检测结果状态"""
        for result in results:
            detection = result['detection']
            has_matches = len(result['feature_matches']) > 0

            # 选择颜色
            color = (0, 255, 0) if has_matches else (0, 0, 255)  # 绿色：匹配，红色：不匹配

            # 绘制边界框
            cv2.rectangle(image,
                         (detection.bbox[0], detection.bbox[1]),
                         (detection.bbox[2], detection.bbox[3]),
                         color, 2)

            # 添加状态标签
            status = "VALID" if has_matches else "INVALID"
            label = f"{status} ({detection.confidence:.2f})"

            # 计算文本位置
            text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            text_x = detection.bbox[0]
            text_y = detection.bbox[1] - 10 if detection.bbox[1] > 30 else detection.bbox[3] + 25

            # 绘制文本背景
            cv2.rectangle(image,
                         (text_x, text_y - text_size[1] - 5),
                         (text_x + text_size[0] + 5, text_y + 5),
                         color, -1)

            # 绘制文本
            cv2.putText(image, label, (text_x + 2, text_y - 2),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return image

    def _add_statistics(self, image: np.ndarray, results: List[Dict],
                       features: List[GeoFeature]) -> np.ndarray:
        """添加统计信息"""
        valid_count = sum(1 for r in results if r['feature_matches'])
        invalid_count = len(results) - valid_count

        # 创建半透明背景
        overlay = image.copy()
        cv2.rectangle(overlay, (10, 10), (350, 120), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, image, 0.3, 0, image)

        # 添加统计文本
        texts = [
            f"Features: {len(features)}",
            f"Valid Detections: {valid_count}",
            f"Invalid Detections: {invalid_count}",
            f"Total Detections: {len(results)}"
        ]

        for i, text in enumerate(texts):
            y_pos = 35 + i * 20
            cv2.putText(image, text, (20, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return image

    def clear_features(self):
        """清空所有特征"""
        self.features.clear()

    def get_feature_by_name(self, name: str) -> Optional[GeoFeature]:
        """根据名称获取特征"""
        for feature in self.features:
            if feature.name == name:
                return feature
        return None

    def remove_feature(self, name: str) -> bool:
        """移除指定特征"""
        for i, feature in enumerate(self.features):
            if feature.name == name:
                del self.features[i]
                return True
        return False

    def __del__(self):
        """析构函数"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)


# 工具函数
def create_camera_params(fx: float, fy: float, cx: float, cy: float,
                        width: int, height: int) -> CameraParams:
    """创建相机参数"""
    return CameraParams(fx=fx, fy=fy, cx=cx, cy=cy, width=width, height=height)


def create_drone_state(lat: float, lon: float, alt: float,
                      pitch: float = 0.0, yaw: float = 0.0, roll: float = 0.0) -> DroneState:
    """创建无人机状态"""
    return DroneState(
        latitude=lat, longitude=lon, altitude=alt,
        pitch=np.radians(pitch), yaw=np.radians(yaw), roll=np.radians(roll)
    )


def create_detection(bbox: Tuple[int, int, int, int], confidence: float,
                    class_id: int) -> DetectionResult:
    """创建检测结果"""
    return DetectionResult(bbox=bbox, confidence=confidence, class_id=class_id)
