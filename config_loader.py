"""
配置加载器和批处理工具
支持从JSON/YAML文件加载地理特征配置
"""

import json
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path
import numpy as np

from geo_projection_system import (
    GeoProjectionSystem, FeatureType, FeatureStyle, CameraParams, DroneState
)


class ConfigLoader:
    """配置加载器"""
    
    @staticmethod
    def load_from_json(file_path: str) -> Dict[str, Any]:
        """从JSON文件加载配置"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def load_from_yaml(file_path: str) -> Dict[str, Any]:
        """从YAML文件加载配置"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    @staticmethod
    def load_config(file_path: str) -> Dict[str, Any]:
        """自动识别文件格式并加载配置"""
        path = Path(file_path)
        
        if path.suffix.lower() == '.json':
            return ConfigLoader.load_from_json(file_path)
        elif path.suffix.lower() in ['.yaml', '.yml']:
            return ConfigLoader.load_from_yaml(file_path)
        else:
            raise ValueError(f"不支持的配置文件格式: {path.suffix}")


class FeatureConfigParser:
    """特征配置解析器"""
    
    @staticmethod
    def parse_camera_params(config: Dict[str, Any]) -> CameraParams:
        """解析相机参数"""
        cam_config = config['camera']
        return CameraParams(
            fx=cam_config['fx'],
            fy=cam_config['fy'],
            cx=cam_config['cx'],
            cy=cam_config['cy'],
            width=cam_config['width'],
            height=cam_config['height'],
            distortion=np.array(cam_config.get('distortion', [])) if cam_config.get('distortion') else None
        )
    
    @staticmethod
    def parse_drone_state(config: Dict[str, Any]) -> DroneState:
        """解析无人机状态"""
        drone_config = config['drone']
        return DroneState(
            latitude=drone_config['latitude'],
            longitude=drone_config['longitude'],
            altitude=drone_config['altitude'],
            pitch=np.radians(drone_config.get('pitch', 0.0)),
            yaw=np.radians(drone_config.get('yaw', 0.0)),
            roll=np.radians(drone_config.get('roll', 0.0))
        )
    
    @staticmethod
    def parse_feature_style(style_config: Dict[str, Any]) -> FeatureStyle:
        """解析特征样式"""
        return FeatureStyle(
            color=tuple(style_config.get('color', [0, 255, 0])),
            thickness=style_config.get('thickness', 2),
            fill=style_config.get('fill', False),
            fill_color=tuple(style_config['fill_color']) if style_config.get('fill_color') else None,
            alpha=style_config.get('alpha', 0.3),
            show_direction=style_config.get('show_direction', False),
            buffer_distance=style_config.get('buffer_distance', 20.0)
        )
    
    @staticmethod
    def parse_features(config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析地理特征列表"""
        features = []
        
        for feature_config in config.get('features', []):
            # 解析特征类型
            feature_type_str = feature_config['type'].upper()
            feature_type = FeatureType(feature_type_str.lower())
            
            # 解析坐标
            coordinates = [tuple(coord) for coord in feature_config['coordinates']]
            
            # 解析样式
            style = FeatureConfigParser.parse_feature_style(
                feature_config.get('style', {})
            )
            
            features.append({
                'coordinates': coordinates,
                'feature_type': feature_type,
                'style': style,
                'name': feature_config.get('name', ''),
                'enabled': feature_config.get('enabled', True)
            })
        
        return features


class ProjectionSystemBuilder:
    """投影系统构建器"""
    
    @staticmethod
    def build_from_config(config_file: str) -> tuple:
        """从配置文件构建投影系统"""
        
        # 加载配置
        config = ConfigLoader.load_config(config_file)
        
        # 解析相机参数
        camera_params = FeatureConfigParser.parse_camera_params(config)
        
        # 解析无人机状态
        drone_state = FeatureConfigParser.parse_drone_state(config)
        
        # 创建投影系统
        max_workers = config.get('system', {}).get('max_workers', 4)
        projection_system = GeoProjectionSystem(camera_params, max_workers)
        
        # 添加特征
        features = FeatureConfigParser.parse_features(config)
        for feature_data in features:
            if feature_data['enabled']:
                projection_system.add_feature(
                    coordinates=feature_data['coordinates'],
                    feature_type=feature_data['feature_type'],
                    style=feature_data['style'],
                    name=feature_data['name']
                )
        
        return projection_system, camera_params, drone_state


def create_sample_config():
    """创建示例配置文件"""
    
    config = {
        "camera": {
            "fx": 1000.0,
            "fy": 1000.0,
            "cx": 640.0,
            "cy": 360.0,
            "width": 1280,
            "height": 720,
            "distortion": [0.1, -0.2, 0.0, 0.0, 0.0]
        },
        "drone": {
            "latitude": 39.9042,
            "longitude": 116.4074,
            "altitude": 100.0,
            "pitch": -30.0,
            "yaw": 0.0,
            "roll": 0.0
        },
        "system": {
            "max_workers": 4
        },
        "features": [
            {
                "name": "whitelist_area",
                "type": "polygon",
                "enabled": True,
                "coordinates": [
                    [39.9050, 116.4060],
                    [39.9050, 116.4088],
                    [39.9034, 116.4088],
                    [39.9034, 116.4060]
                ],
                "style": {
                    "color": [0, 255, 0],
                    "thickness": 3,
                    "fill": True,
                    "fill_color": [0, 255, 0],
                    "alpha": 0.2
                }
            },
            {
                "name": "patrol_route",
                "type": "trajectory",
                "enabled": True,
                "coordinates": [
                    [39.9048, 116.4062],
                    [39.9046, 116.4070],
                    [39.9042, 116.4078],
                    [39.9038, 116.4086],
                    [39.9036, 116.4084]
                ],
                "style": {
                    "color": [255, 255, 0],
                    "thickness": 4,
                    "show_direction": True,
                    "buffer_distance": 25.0
                }
            },
            {
                "name": "no_fly_zone",
                "type": "boundary",
                "enabled": True,
                "coordinates": [
                    [39.9055, 116.4055],
                    [39.9055, 116.4093],
                    [39.9029, 116.4093],
                    [39.9029, 116.4055]
                ],
                "style": {
                    "color": [0, 0, 255],
                    "thickness": 5,
                    "buffer_distance": 50.0
                }
            }
        ]
    }
    
    return config


def save_sample_config(file_path: str = "sample_config.json"):
    """保存示例配置文件"""
    config = create_sample_config()
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"示例配置文件已保存到: {file_path}")


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, projection_system: GeoProjectionSystem, 
                 camera_params: CameraParams, drone_state: DroneState):
        self.projection_system = projection_system
        self.camera_params = camera_params
        self.drone_state = drone_state
    
    def process_image_batch(self, image_paths: List[str], 
                           detection_results: List[List], 
                           output_dir: str = "output"):
        """批量处理图像"""
        import cv2
        from pathlib import Path
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        results = []
        
        for i, (img_path, detections) in enumerate(zip(image_paths, detection_results)):
            print(f"处理图像 {i+1}/{len(image_paths)}: {img_path}")
            
            # 加载图像
            image = cv2.imread(img_path)
            if image is None:
                print(f"无法加载图像: {img_path}")
                continue
            
            # 处理图像
            result_image, detection_results = self.projection_system.process_frame(
                image, detections, self.drone_state
            )
            
            # 保存结果
            output_file = output_path / f"result_{i+1:04d}.jpg"
            cv2.imwrite(str(output_file), result_image)
            
            results.append({
                'input_path': img_path,
                'output_path': str(output_file),
                'detection_results': detection_results
            })
        
        return results


if __name__ == "__main__":
    # 创建示例配置文件
    save_sample_config()
    
    # 演示从配置文件构建系统
    print("从配置文件构建投影系统...")
    projection_system, camera_params, drone_state = ProjectionSystemBuilder.build_from_config("sample_config.json")
    
    print(f"成功构建投影系统，包含 {len(projection_system.features)} 个特征")
    for feature in projection_system.features:
        print(f"  - {feature.name}: {feature.feature_type.value}")
